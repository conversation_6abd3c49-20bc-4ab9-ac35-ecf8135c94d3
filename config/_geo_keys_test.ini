[azureGeocoder]
primaryKey = "azure-geo-primary-key"
secondaryKey = "azure-geo-secondary-key"

[geoCoderService]
allowedIPs = [ "127.0.0.1" ]
geoip = false
token = "aUIh0DMLQY"
url = "http://d1w.realmaster.cn/geocodingService"
useRemoteService = false

[google]
andriod = "AIzaSyBOslrM1NF12A_SX_SnhFXKoBxmfdeD7Gc" # android native map
andriodAnalytics = "AIzaSyB3j8f64si2jzbsAxCAL5FAmb64GJS-2A8" # android only
browser = "AIzaSyCcnrOBfqHK-eOMGcY8tTyEe5mNc7S_WeU" # for commute:direction, place,js.TODO: change commute to mapbox,wecard
geocoderServer = "AIzaSyDfGNii_C0VMC6ku7bWCmeDOcd0RIzzfyI" # server geocoder only, TODO:need restrict ip
housemax = "AIzaSyC8WWwQzKqHPLTY5QHH82vSMM6egsJesmU" # 3rd paty
ios = "AIzaSyAsj6IN_2_1hPr5f_niGK3sUvViIX7omqg" # ios native google map,
iosAnalytics = "AIzaSyBCqhaFdE2PJWB1pPSdBy4rkRwn8TtbGGg" # native, restrict to app
oldNative = "AIzaSyClYqxKKg2WdhFE9UDKA9ekFYHDIMLfdLA" # place(ios,android,sdk),map, geocoding,direction, !deprecated, will be removed in new versions
x_iosTest = "AIzaSyDg3M9-IWfpswsto_nXAIX9ESHt987-SvY" # restrict ios bundle, TODO:@rain, test if working

[here]
key = "gI6hBaOR4eBPRKgiNE4qIUUDY6UZ-ZlAlZxZt2o5LZE"

[mapbox]
address = "pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A"
app = "pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A"
housemax = "pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A"
web = "pk.eyJ1IjoicmVhbG1hc3RlciIsImEiOiJjazV3czQxdjQxeW9kM21tanpjcjQ5bnV6In0.MZWvPMfG2bFEOHczsP1U6A"

[mapquest]
key = "********************************"
