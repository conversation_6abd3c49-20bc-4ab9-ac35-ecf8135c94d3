[abTest]
homepage = true
ratioOfA = 0.5

[lib]
middleware = "${srcPath}/middleware"
vendor = "${srcPath}/vendor"

[limitAccessRate]
AccessPerSessionLimit = 9_000
AvgAccessPerSessionIP = 21
SessionPerIPThreshold = 181

[media]
path = "${srcPath}/webroot/public"
wwwbase = "http://realmaster.com"

[profiler]
file = "${configPath}/logs/profile.log"
freq = 60_000

[server]
host = "127.0.0.1"
noch = true
pid = "${configPath}/logs/cmate.pid"
port = 8_080
root = "${srcPath}/webroot"
stderr = "${configPath}/logs/err.log"
stdout = "${configPath}/logs/std.log"
title = "cmate"

[serverBase]
#exclude_apps = ['<%this.srcPath%>/apps/80_sites/WebRMBC','<%this.srcPath%>/apps/80_sites/WebRMAvionHome','<%this.srcPath%>/apps/80_sites/]
#logLevel = 1 will overwrite all the batch logLevel
androidNoHTTPS = false
appHostUrl = "https://realmaster.com"
base = "."
bcreBaseDir = "/opt/data/bcre"
canonicalHost = "https://www.realmaster.com"
chinaMode = false
creaBaseDir = "/mnt/mls/sync/crea"
createRNIIndex = true
debugThreshhold = 3
developer_mode = true
forceHTTPS = false
headerfile = "header.coffee"
isUnitTestServer = false
masterMode = true
migration = "${srcPath}/migrate"
noSitemap = 1
ofe = false
onDMergeDateSpanForDiffSid = 86_400_000 # 1 * 24 * 3600000
onDMergeDateSpanForDiffSidAndNoPho = 604_800_000 # 7 * 24 * 3600000
onDMergeDateSpanForSameSid = 1_728_000_000 # 20 * 24 * 3600000
onDRangeExpansionValue = 432_000_000 # 5 * 24 * 3600000
proxy_header = "x-real-ip"
satelliteMode = false
skipSendLeadToCrm = true
srcGoAppPath = "${srcGoAppPath}"
srcGoBasePath = "${srcGoBasePath}"
srcPath = "${srcPath}"
themes = "${srcPath}/themes"
themesAutoReload = true
trebBaseDir = "../treb"
use3rdPic = false
useHTTPS = true
verbose = 3
vhost = "${configPath}/built/config/vhost.ini"
wpBlogsUrl = "https://www.realmaster.com/wp-json/wp/v2/posts?_embed"
wwwDomain = "app.test:8080"

[session]
collection = "sess"
ignore = [ "alive", "sendGrid" ]
lasting = 3_600
maxAge = 3_600
name = "App"
namespace = "Rmt"
secret = "s_ec-re"
store = "sessionRedisStore"
verbose = 0

  [session.redis]
  host = ""
  port = 6_379

[socket]
path = "/socket"

[static]
cache = true
max_age = 31_557_600_000
max_cache_size = 524_288 # 1024 * 512
public = "${srcPath}/webroot/public"
verbose = 1

[trebManualImport]
relayDomain = "<http://d9.realmaster.com>"
relayImport = true
skipAuth = true
